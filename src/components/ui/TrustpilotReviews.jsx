import { useEffect } from "react";

const TrustpilotReviews = () => {
  useEffect(() => {
    // Trustpilot widget script - only load if not already loaded
    if (!document.querySelector('script[src*="tp.widget.bootstrap.min.js"]')) {
      const script = document.createElement("script");
      script.src =
        "//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js";
      script.async = true;
      document.body.appendChild(script);

      return () => {
        // Only remove if we added it
        if (document.body.contains(script)) {
          document.body.removeChild(script);
        }
      };
    }
  }, []);

  return (
    <div className="mt-16 mb-12">
      <h2 className="text-3xl sm:text-4xl font-bold text-blue-900 mb-10 text-center">
        Thousands of businesses choose us
      </h2>
      <div>
        <div
          className="trustpilot-widget"
          data-locale="en-US"
          data-template-id="54ad5defc6454f065c28af8b"
          data-businessunit-id="66d0b7dd321310cee73cdf4b"
          data-style-height="240px"
          data-style-width="100%"
          data-stars="1,2,3,4,5"
          data-theme="light"
          data-review-languages="en"
        >
          {/* Fallback content while loading */}
          <div className="bg-gray-50 p-8 rounded-sm shadow-sm animate-pulse">
            <div className="flex justify-center space-x-8">
              {[1, 2, 3, 4, 5].map((item) => (
                <div
                  key={item}
                  className="bg-white p-4 rounded-sm shadow-sm w-64"
                >
                  <div className="flex mb-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <div
                        key={star}
                        className="w-4 h-4 bg-gray-300 rounded-sm mr-1"
                      ></div>
                    ))}
                  </div>
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded mb-1"></div>
                  <div className="h-3 bg-gray-300 rounded mb-1"></div>
                  <div className="h-3 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-300 rounded mt-2 w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrustpilotReviews;
